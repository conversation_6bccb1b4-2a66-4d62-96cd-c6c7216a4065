import asyncio
import unittest
from unittest.mock import patch, MagicMock

from langchain_core.tools import ToolException

from app.tools.get_variants_tool import (
    get_variants_tool,
    _get_variants_tool,
    _aget_variants_tool,
    _filter_variants,
    _format_variant_output,
    VariantSchema,
)


class TestGetVariantsTool(unittest.TestCase):
    def setUp(self):
        """Set up test data"""
        self.sample_variants_data = [
            {
                "variant_code": "H1B02N41L1A/T",
                "variant_name": "BeAT Deluxe CBS",
                "model_name": "BeAT",
                "price": 18500000,
                "url_image": "https://example.com/image1.jpg",
            },
            {
                "variant_code": "H1B02N41L2A/T",
                "variant_name": "BeAT Street CBS",
                "model_name": "BeAT",
                "price": 17800000,
                "url_image": "https://example.com/image2.jpg",
            },
        ]

        self.sample_api_response = {
            "data": self.sample_variants_data,
            "status": "success",
        }

        self.expected_csv_output = (
            "variant_code,variant_name,model_name,price,product_link\n"
            "H1B02N41L1A/T,BeAT Deluxe CBS,BeAT,18500000,https://amartahonda.com/baru/bandung/H1B02N41L1AT\n"
            "H1B02N41L2A/T,BeAT Street CBS,BeAT,17800000,https://amartahonda.com/baru/bandung/H1B02N41L2AT"
        )

    def test_variant_schema_validation(self):
        """Test VariantSchema validation"""
        # Valid schema
        valid_schema = VariantSchema(model_name="BeAT", city_group="bandung")
        self.assertEqual(valid_schema.model_name, "BeAT")
        self.assertEqual(valid_schema.city_group, "bandung")

        # Default city_group
        schema_with_default = VariantSchema(model_name="Vario 125")
        self.assertEqual(schema_with_default.city_group, "bandung")

    def test_filter_variants_valid_data(self):
        """Test _filter_variants with valid data"""
        result = _filter_variants(self.sample_variants_data)
        self.assertEqual(len(result), 2)
        self.assertEqual(result, self.sample_variants_data)

    def test_filter_variants_empty_data(self):
        """Test _filter_variants with empty data"""
        result = _filter_variants([])
        self.assertEqual(result, [])

        result = _filter_variants(None)
        self.assertEqual(result, [])

    def test_filter_variants_invalid_data(self):
        """Test _filter_variants with invalid data structure"""
        invalid_variants = [
            {"variant_code": "H1B02N41L1A/T"},  # Missing required fields
            {
                "variant_code": "H1B02N41L2A/T",
                "variant_name": "BeAT Street CBS",
                "model_name": "BeAT",
                "price": 17800000,
            },  # Valid variant
        ]

        result = _filter_variants(invalid_variants)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["variant_code"], "H1B02N41L2A/T")

    def test_format_variant_output_valid_data(self):
        """Test _format_variant_output with valid data"""
        result = _format_variant_output(self.sample_variants_data, "bandung")
        self.assertEqual(result, self.expected_csv_output)

    def test_format_variant_output_empty_data(self):
        """Test _format_variant_output with empty data"""
        result = _format_variant_output([], "bandung")
        expected = "variant_code,variant_name,model_name,price,product_link\nTidak ada varian yang tersedia untuk model ini"
        self.assertEqual(result, expected)

    def test_format_variant_output_with_commas(self):
        """Test _format_variant_output with data containing commas"""
        variants_with_commas = [
            {
                "variant_code": "H1B02N41L1A/T",
                "variant_name": "BeAT Deluxe, CBS",
                "model_name": "BeAT, Special",
                "price": 18500000,
            }
        ]

        result = _format_variant_output(variants_with_commas, "bandung")
        self.assertIn("BeAT Deluxe; CBS", result)
        self.assertIn("BeAT; Special", result)

    def test_format_variant_output_url_generation(self):
        """Test URL generation in _format_variant_output"""
        result = _format_variant_output(self.sample_variants_data, "jakarta")
        self.assertIn("https://amartahonda.com/baru/jakarta/H1B02N41L1AT", result)
        self.assertIn("https://amartahonda.com/baru/jakarta/H1B02N41L2AT", result)

    @patch("app.tools.get_variants_tool.CatalogueService")
    def test_get_variants_tool_success(self, mock_variant_service):
        """Test _get_variants_tool with successful API response"""
        # Setup mock
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance
        mock_service_instance.get_variants.return_value = self.sample_api_response

        # Call function
        result = _get_variants_tool("BeAT", "bandung")

        # Assertions
        self.assertEqual(result, self.expected_csv_output)
        mock_service_instance.get_variants.assert_called_once_with(
            city_group="bandung", model_name="BeAT"
        )

    @patch("app.tools.get_variants_tool.CatalogueService")
    async def test_aget_variants_tool_success(self, mock_variant_service):
        """Test _aget_variants_tool with successful API response"""
        # Setup mock
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance

        # Create an async mock for aget_variants
        async def mock_aget_variants(*args, **kwargs):
            return self.sample_api_response

        mock_service_instance.aget_variants = mock_aget_variants

        # Call function
        result = await _aget_variants_tool("BeAT", "bandung")

        # Assertions
        self.assertEqual(result, self.expected_csv_output)

    def test_get_variants_tool_empty_model_name(self):
        """Test _get_variants_tool with empty model name"""
        with self.assertRaises(ToolException) as context:
            _get_variants_tool("", "bandung")

        self.assertIn("model_name tidak boleh kosong", str(context.exception))

        with self.assertRaises(ToolException) as context:
            _get_variants_tool("   ", "bandung")

        self.assertIn("model_name tidak boleh kosong", str(context.exception))

    async def test_aget_variants_tool_empty_model_name(self):
        """Test _aget_variants_tool with empty model name"""
        with self.assertRaises(ToolException) as context:
            await _aget_variants_tool("", "bandung")

        self.assertIn("model_name tidak boleh kosong", str(context.exception))

    def test_get_variants_tool_empty_city_group_fallback(self):
        """Test _get_variants_tool with empty city_group falls back to default"""
        with patch(
            "app.tools.get_variants_tool.CatalogueService"
        ) as mock_variant_service:
            mock_service_instance = MagicMock()
            mock_variant_service.return_value = mock_service_instance
            mock_service_instance.get_variants.return_value = self.sample_api_response

            # Test with empty city_group
            result = _get_variants_tool("BeAT", "")

            # Should call with default "bandung"
            mock_service_instance.get_variants.assert_called_once_with(
                city_group="bandung", model_name="BeAT"
            )

    @patch("app.tools.get_variants_tool.CatalogueService")
    def test_get_variants_tool_no_variants_data(self, mock_variant_service):
        """Test _get_variants_tool when API returns no variants"""
        # Setup mock with empty data
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance
        mock_service_instance.get_variants.return_value = {"data": []}

        # Call function
        result = _get_variants_tool("NonExistentModel", "bandung")

        # Should return appropriate message
        expected = "variant_code,variant_name,model_name,price,product_link\nTidak ada varian tersedia untuk model 'NonExistentModel' di area 'bandung'"
        self.assertEqual(result, expected)

    @patch("app.tools.get_variants_tool.CatalogueService")
    def test_get_variants_tool_empty_api_response(self, mock_variant_service):
        """Test _get_variants_tool when API returns empty response"""
        # Setup mock with None response
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance
        mock_service_instance.get_variants.return_value = None

        # Call function and expect ToolException
        with self.assertRaises(ToolException) as context:
            _get_variants_tool("BeAT", "bandung")

        self.assertIn("Response API kosong atau tidak valid", str(context.exception))

    @patch("app.tools.get_variants_tool.CatalogueService")
    def test_get_variants_tool_api_exception(self, mock_variant_service):
        """Test _get_variants_tool when API raises exception"""
        # Setup mock to raise exception
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance
        mock_service_instance.get_variants.side_effect = Exception("API Error")

        # Call function and expect ToolException
        with self.assertRaises(ToolException) as context:
            _get_variants_tool("BeAT", "bandung")

        error_message = str(context.exception)
        self.assertIn("Gagal mengambil data varian untuk model 'BeAT'", error_message)
        self.assertIn("API Error", error_message)

    def test_get_variants_tool_input_normalization(self):
        """Test input normalization (trimming and case conversion)"""
        with patch(
            "app.tools.get_variants_tool.CatalogueService"
        ) as mock_variant_service:
            mock_service_instance = MagicMock()
            mock_variant_service.return_value = mock_service_instance
            mock_service_instance.get_variants.return_value = self.sample_api_response

            # Test with spaces and mixed case
            result = _get_variants_tool("  BeAT  ", "  BANDUNG  ")

            # Should normalize inputs
            mock_service_instance.get_variants.assert_called_once_with(
                city_group="bandung", model_name="BeAT"
            )

    @patch("app.tools.get_variants_tool.CatalogueService")
    def test_structured_tool_invoke_success(self, mock_variant_service):
        """Test the actual StructuredTool invoke method"""
        # Setup mock
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance
        mock_service_instance.get_variants.return_value = self.sample_api_response

        # Call the tool
        result = get_variants_tool.invoke(
            {"model_name": "BeAT", "city_group": "bandung"}
        )

        # Assertions
        self.assertEqual(result, self.expected_csv_output)

    @patch("app.tools.get_variants_tool.CatalogueService")
    async def test_structured_tool_ainvoke_success(self, mock_variant_service):
        """Test the actual StructuredTool ainvoke method"""
        # Setup mock
        mock_service_instance = MagicMock()
        mock_variant_service.return_value = mock_service_instance

        # Create an async mock for aget_variants
        async def mock_aget_variants(*args, **kwargs):
            return self.sample_api_response

        mock_service_instance.aget_variants = mock_aget_variants

        # Call the tool
        result = await get_variants_tool.ainvoke(
            {"model_name": "BeAT", "city_group": "bandung"}
        )

        # Assertions
        self.assertEqual(result, self.expected_csv_output)


def run_async_test(coro):
    """Helper function to run async tests"""
    return asyncio.run(coro)


if __name__ == "__main__":
    # Run the tests
    test = TestGetVariantsTool()

    # Run sync tests
    test.setUp()
    test.test_variant_schema_validation()
    test.test_filter_variants_valid_data()
    test.test_filter_variants_empty_data()
    test.test_filter_variants_invalid_data()
    test.test_format_variant_output_valid_data()
    test.test_format_variant_output_empty_data()
    test.test_format_variant_output_with_commas()
    test.test_format_variant_output_url_generation()
    test.test_get_variants_tool_success()
    test.test_get_variants_tool_empty_model_name()
    test.test_get_variants_tool_empty_city_group_fallback()
    test.test_get_variants_tool_no_variants_data()
    test.test_get_variants_tool_empty_api_response()
    test.test_get_variants_tool_api_exception()
    test.test_get_variants_tool_input_normalization()
    test.test_structured_tool_invoke_success()

    # Run async tests
    run_async_test(test.test_aget_variants_tool_success())
    run_async_test(test.test_aget_variants_tool_empty_model_name())
    run_async_test(test.test_structured_tool_ainvoke_success())

    print("All tests passed!")
