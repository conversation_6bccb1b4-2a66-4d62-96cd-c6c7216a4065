# Dokumentasi `get_variants_tool`

Dokumentasi ini menjelaskan cara kerja dan penggunaan dari `get_variants_tool`. Tool ini berfungsi untuk mendapatkan informasi varian motor dari satu atau lebih model spesifik.

## Deskripsi

Tool `get_variants` dirancang untuk mengambil detail lengkap semua varian dari model-model motor tertentu berdasarkan lokasi (grup kota). Informasi yang diambil meliputi harga, nama varian, kode varian, tautan produk, dan deskripsi. Kode varian (`variant_code`) yang dihasilkan oleh tool ini adalah prasyarat **WAJIB** untuk menjalankan simulasi kredit menggunakan tool `get_credit_simulation`.

### Fungsi Utama

- **Mengambil Varian**: Mendapatkan semua varian yang tersedia untuk satu atau lebih model motor.
- **Informasi Detail**: <PERSON><PERSON><PERSON><PERSON> harga, nama varian, kode varian, tautan produk, dan deskripsi.
- **<PERSON><PERSON><PERSON>lkan <PERSON>arian**: Menghasilkan `variant_code` yang esensial untuk simulasi kredit.
- **Penyesuaian Lokasi**: Hasil disesuaikan berdasarkan `city_group` yang diberikan.
- **Dukungan Multi-Organisasi**: Mendukung berbagai organisasi seperti "amarta" dan "vinfast".

## Kapan Harus Digunakan

Tool ini harus digunakan dalam skenario berikut:

1.  **Pertanyaan Harga**: Ketika pengguna menanyakan harga motor.
    - *Contoh: "Harga Vario 125 berapa?"*
2.  **Melihat Pilihan Varian**: Ketika pengguna ingin tahu tipe atau pilihan warna yang tersedia.
    - *Contoh: "BeAT ada tipe apa saja?"*
3.  **Sebelum Simulasi Kredit**: **WAJIB** digunakan sebelum memanggil tool `get_credit_simulation` untuk mendapatkan `variant_code`.
4.  **Melihat Spesifikasi**: Ketika pengguna meminta detail atau spesifikasi motor.
    - *Contoh: "Spek lengkap Vario 125?"*
5.  **Membandingkan Model**: Ketika pengguna ingin membandingkan varian dari beberapa model sekaligus.
    - *Contoh: "Apa saja pilihan BeAT dan Scoopy di Jakarta?"*

## Parameter Input

Tool ini menerima dua parameter input yang didefinisikan dalam `VariantSchema`.

| Parameter     | Tipe          | Deskripsi                                                                 | Contoh                                             |
|---------------|---------------|---------------------------------------------------------------------------|----------------------------------------------------|
| `model_names` | `List[str]`   | Daftar nama model motor yang valid. **Harus** sesuai dengan daftar model yang ada. | `["BeAT"]`, `["Vario 125", "PCX 160"]`, `["Scoopy"]` |
| `city_group`  | `str`         | Grup kota untuk menentukan harga. Defaultnya adalah `"bandung"`.          | `"bandung"`, `"jakarta"`, `"surabaya"`             |

### Prasyarat Wajib

1.  `model_names` harus berisi nama-nama model yang valid dan ditulis dengan kapitalisasi yang benar (contoh: 'BeAT', bukan 'beat').
2.  `city_group` harus dalam format *lowercase* (contoh: 'bandung').

## Format Output

Tool ini mengembalikan data dalam format **daftar vertikal** yang mudah dibaca dengan informasi lengkap untuk setiap varian.

**Contoh Output:**

```
1. BeAT Deluxe CBS
   • Model: BeAT
   • Kode Varian: H1B02N41L1A/T
   • Harga OTR: Rp 18.500.000
   • Link Produk: https://amartahonda.com/baru/bandung/H1B02N41L1AT
   • Deskripsi: Deskripsi varian motor

2. BeAT Street CBS
   • Model: BeAT
   • Kode Varian: H1B02N41L2A/T
   • Harga OTR: Rp 17.800.000
   • Link Produk: https://amartahonda.com/baru/bandung/H1B02N41L2AT
   • Deskripsi: Deskripsi varian motor
```

## Contoh Penggunaan

- **User bertanya harga satu model:**
  - *Input*: `{"model_names": ["Vario 125"], "city_group": "bandung"}`
- **User bertanya varian beberapa model:**
  - *Input*: `{"model_names": ["BeAT", "Scoopy"], "city_group": "jakarta"}`

## Alur Penggunaan Umum

1.  Pengguna menyebutkan satu atau lebih model motor.
2.  Panggil `get_variants_tool` dengan `model_names` yang sesuai.
3.  Tampilkan hasil varian dan harga kepada pengguna dalam format daftar yang mudah dibaca.
4.  Jika pengguna ingin melanjutkan dengan simulasi kredit, ambil `variant_code` dari output dan gunakan sebagai input untuk tool `get_credit_simulation`.

## Penanganan Error

- **Model tidak ditemukan**: Periksa kembali ejaan dan kapitalisasi `model_names`.
- **City group tidak valid**: Pastikan `city_group` menggunakan format *lowercase*.
- **Error API**: Coba lagi setelah beberapa saat atau gunakan `city_group` default ('bandung').

Tool ini akan mengeluarkan `ToolException` jika terjadi kesalahan, dengan pesan yang deskriptif untuk membantu diagnosis masalah.