# Connect to Admin Tool

## Deskripsi

Tool `connect_to_admin` berfungsi untuk mengalihkan percakapan dari AI ke agen manusia (admin/CS) ketika pengguna memintanya. Tool ini akan menonaktifkan balasan dari AI dan memberikan notifikasi bahwa permintaan sedang diproses.

## Komponen

Fitur ini terdiri dari beberapa komponen:

1.  **Tool**: `connect_to_admin.py`
    *   Mendefinisikan `StructuredTool` dengan nama `connect_to_admin`.
    *   Menggunakan `ConnectToAdminSchema` untuk validasi input.
    *   Memanggil service `turn_off_ai_reply` untuk menonaktifkan AI di backend.
    *   Mengembalikan `Command` untuk mengupdate state LangGraph.

2.  **Service**: `turn_off_ai_reply_service.py`
    *   Menyediakan fungsi `turn_off_ai_reply` (async) dan `turn_off_ai_reply_sync` (sync) untuk berkomunikasi dengan API yang menonaktifkan AI.

3.  **Schema**: `ConnectToAdminSchema`
    *   Model Pydantic yang mendefinisikan input untuk tool.
    *   Tool ini tidak memerlukan parameter input apapun.

## Alur Kerja

1.  Pengguna mengirim pesan yang mengindikasikan keinginan untuk berbicara dengan admin (misalnya, "Saya mau bicara dengan CS").
2.  LLM akan memanggil tool `connect_to_admin` tanpa parameter apapun.
3.  Tool `connect_to_admin` akan dieksekusi.
4.  Fungsi `_connect_to_admin` di dalam tool akan memanggil `turn_off_ai_reply_service` untuk menonaktifkan AI melalui API.
5.  Tool mengembalikan `Command` yang berisi:
    *   `ToolMessage` dengan pesan konfirmasi untuk pengguna.
    *   Update state: `connected_to_admin` menjadi `True` dan `turn_completed` menjadi `True`.
6.  Agent akan berhenti membalas, dan percakapan diambil alih oleh admin.

## Penggunaan

### Contoh Pemanggilan Tool

```python
from app.tools.connect_to_admin_tool import connect_to_admin_tool

# Contoh pemanggilan tool
result = await connect_to_admin_tool.ainvoke(
    {},  # Tool tidak memerlukan parameter apapun
    config={
        "configurable": {
            "chatroom_path": "/chatrooms/some_chat_id"
        }
    }
)

print(result)
```

### Contoh Pertanyaan Pengguna

Tool ini dirancang untuk menangani permintaan seperti:

*   "Saya ingin bicara dengan manusia"
*   "Hubungkan saya ke customer service"
*   "Saya butuh bantuan manusia"
*   "Tolong sambungkan ke operator"
*   "Bisa bicara dengan CS?"
*   "Saya mau komplain ke manusia"
*   "AI ini tidak membantu, saya mau bicara dengan orang"
*   "Hubungkan ke tim support"
*   "Saya perlu bantuan langsung dari manusia"
*   "Bisa transfer ke agen?"

## Endpoint API

Service ini menggunakan endpoint API berikut:

```
POST /public/toggle-agent-ai-reply
```

Dengan payload:

```json
{
  "chatRoomPath": "/path/to/chat_room",
  "agentAiReply": false,
  "conversationResume": "User requested connection to admin"
}
```

## Catatan Implementasi

*   Tool ini harus dipanggil ketika ada indikasi yang jelas dari pengguna untuk berbicara dengan manusia.
*   Tool ini tidak memerlukan parameter input apapun, sehingga mudah digunakan.
*   Flag `connected_to_admin` di state memastikan bahwa agent tidak akan memberikan balasan lebih lanjut setelah tool ini dieksekusi.
*   Sistem akan otomatis menggunakan pesan default "User requested connection to admin" sebagai conversation resume.
