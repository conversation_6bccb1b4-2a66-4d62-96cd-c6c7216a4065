import textwrap
from typing import List, Dict, Any

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import StructuredTool, ToolException
from pydantic import BaseModel, Field

from app.services.catalogue_service import CatalogueService

_DESCRIPTION = textwrap.dedent(
    """
Mendapatkan detail varian motor/mobil berdasarkan model dan kota.

Tool ini digunakan untuk:
- Mendapatkan daftar varian motor/mobil yang tersedia untuk model tertentu
- Menampilkan informasi harga otr, kode varian, dan nama varian
- Melihat link produk untuk setiap varian motor/mobil
- Memfilter varian berdasarkan lokasi/kota tertentu
- Mendapatkan data lengkap varian untuk proses simulasi kredit

Input yang diperlukan:
- model_names: Nama model motor/mobil yang valid dari VARIABEL SISTEM (array string, bisa lebih dari satu)
- city_group: Grup kota dalam format lowercase (string, wajib diisi)

Output:
- Data varian dalam format daftar vertikal yang mudah dibaca
- Setiap varian ditampilkan dengan nomor urut dan informasi lengkap
- Informasi meliputi: nama varian, model, kode varian, harga OTR, dan link produk
- Harga ditampilkan dengan format mata uang Rupiah dan pemisah ribuan
- Error message jika model tidak ditemukan atau tidak ada varian tersedia
- Link produk yang dapat diakses untuk melihat detail lebih lanjut

CONTOH PERTANYAAN:
- "Varian motor BeAT di Jakarta"
- "Pilihan warna motor Vario 125 di Bandung"
- "Tipe motor PCX yang tersedia di Surabaya"
- "Varian motor Scoopy di Medan"
- "Daftar varian motor Honda di Jakarta"
- "Pilihan motor BeAT yang ada"
- "Harga Honda PCX 160 di Surabaya berapa?"
- "Kode varian Honda Scoopy di Medan"
- "Detail varian Honda CB150R di Jakarta"
- "Apa saja pilihan Honda ADV 150 di Bandung?"
"""
)


class VariantSchema(BaseModel):
    """
    Schema untuk input parameter tool get_variants.

    Attributes:
        model_names (List[str]): Nama model motor yang valid
        city_group (str): Grup kota dalam format lowercase untuk lokasi spesifik
    """

    model_names: List[str] = Field(
        ...,
        description="Nama model yang tersedia pada daftar Model",
    )
    city_group: str = Field(
        description="Grup kota dalam format lowercase untuk menentukan lokasi dan harga spesifik.",
    )


def _filter_variants(variants: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Filter dan validasi data varian dari API response.

    Args:
        variants (List[Dict[str, Any]]): List data varian dari API

    Returns:
        List[Dict[str, Any]]: Data varian yang sudah difilter dan divalidasi

    Note:
        Saat ini fungsi ini mengembalikan semua varian tanpa filter,
        tapi bisa dikembangkan untuk filtering berdasarkan kriteria tertentu.
    """
    if not variants:
        return []

    # Validasi struktur data varian
    valid_variants = []
    for variant in variants:
        if all(
            key in variant
            for key in ["variant_code", "variant_name", "model_name", "price"]
        ):
            valid_variants.append(variant)

    return valid_variants


def _format_variant_output(
    variants: List[Dict[str, Any]], city_group: str, organization: str
) -> str:
    """
    Format data varian menjadi daftar vertikal dengan informasi lengkap.

    Args:
        variants (List[Dict[str, Any]]): Data varian yang sudah difilter
        city_group (str): Grup kota untuk membuat product link
        organization (str): Organisasi untuk menentukan format link

    Returns:
        str: Data varian dalam format daftar vertikal
    """
    if not variants:
        return "Tidak ada varian yang tersedia untuk model ini."

    result = []

    for i, variant in enumerate(variants, 1):
        variant_code = variant.get("variant_code", "")
        variant_name = variant.get("variant_name", "")
        model_name = variant.get("model_name", "")
        price = variant.get("price", 0)
        description = variant.get("ai_notes", "")

        # Remove slashes from variant_code to create a valid URL path segment
        variant_code_for_url = variant_code.replace("/", "")

        product_link = ""
        if organization == "amarta":
            product_link = f"https://amartahonda.com/baru/{city_group.lower()}/{variant_code_for_url}"
        elif organization == "vinfast":
            product_link = f"https://amartavinfast.com/variant/{variant_code_for_url}"

        # Format harga dengan pemisah ribuan
        formatted_price = f"Rp {price:,}".replace(",", ".")

        variant_info = f"""
{i}. {variant_name}
   • Model: {model_name}
   • Kode Varian: {variant_code}
   • Harga OTR: {formatted_price}
   • Link Produk: {product_link}
   • Deskripsi: {description}
"""
        result.append(variant_info.strip())

    return "\n\n".join(result)


async def _aget_variants_tool(
    model_names: List[str], city_group: str = "bandung", config: RunnableConfig = None
) -> str:
    """
    Fungsi asinkron untuk mengambil data varian motor.

    Args:
        model_names (List[str]): Nama model motor yang valid
        city_group (str): Grup kota dalam format lowercase

    Returns:
        str: Data varian dalam format daftar vertikal yang mudah dibaca

    Raises:
        ToolException: Jika terjadi error saat mengambil data
    """
    try:
        # Validasi input
        if not model_names:
            raise ToolException("model_names tidak boleh kosong")

        if not city_group or not city_group.strip():
            city_group = "bandung"  # Default fallback

        # Pastikan city_group lowercase
        city_group = city_group.lower().strip()
        model_names = [model.strip() for model in model_names]

        organization = config.get("configurable", {}).get("organization", "amarta")
        service = CatalogueService(organization=organization)
        variants_response = await service.aget_variants(
            city_group=city_group,
            model_names=model_names,
        )

        if not variants_response:
            raise ToolException("Response API kosong atau tidak valid")

        variants_data = variants_response.get("data", [])
        if not variants_data:
            return f"Tidak ada varian tersedia untuk model {', '.join(model_names)} di area '{city_group}'"

        filtered_variants = _filter_variants(variants_data)
        return _format_variant_output(filtered_variants, city_group, organization)

    except ToolException:
        raise
    except Exception as e:
        error_msg = (
            f"Gagal mengambil data varian untuk model {', '.join(model_names)}: {str(e)}. "
            "Pastikan: "
            "1. model_names valid (contoh: ['BeAT', 'Vario 125', 'PCX 160']), "
            "2. city_group dalam format lowercase (contoh: 'bandung'), "
            "3. Koneksi internet stabil"
        )
        raise ToolException(error_msg)


get_variants_tool = StructuredTool.from_function(
    name="get_variants",
    coroutine=_aget_variants_tool,
    args_schema=VariantSchema,
    description=_DESCRIPTION,
    return_direct=False,
)

if __name__ == "__main__":
    import asyncio

    config = RunnableConfig(configurable={"organization": "amarta"})

    async def main():
        result = await get_variants_tool.ainvoke(
            {"model_names": ["BeAT DLX"], "city_group": "bandung"}, config
        )
        print(result)

    asyncio.run(main())
