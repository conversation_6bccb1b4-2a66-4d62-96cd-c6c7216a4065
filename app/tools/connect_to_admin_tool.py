import logging
import textwrap
from datetime import datetime
import pytz

from langchain_core.messages import ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import StructuredTool, InjectedToolCallId
from langgraph.types import Command
from pydantic import BaseModel, Field
from typing_extensions import Annotated

from app.services.turn_off_ai_reply_service import turn_off_ai_reply

_DESCRIPTION = textwrap.dedent(
    """
Menghubungkan user ke admin.

Tool ini digunakan untuk:
- Mengalihkan percakapan ke agen manusia untuk penanganan lebih lanjut
- Memberikan eskalasi layanan ketika AI tidak dapat membantu
- Memfasilitasi komunikasi langsung dengan customer service
- Menangani situasi yang memerlukan intervensi manusia

Tool ini tidak memerlukan input parameter apapun.

Output:
- Konfirmasi bahwa AI telah dinonaktifkan dengan sukses

CONTOH PERTANYAAN:
- "Saya ingin bicara dengan manusia"
- "Hubungkan saya ke customer service"
- "Saya butuh bantuan manusia"
- "Tolong sambungkan ke operator"
- "Bisa bicara dengan CS?"
- "Saya mau komplain ke manusia"
- "AI ini tidak membantu, saya mau bicara dengan orang"
- "Hubungkan ke tim support"
- "Saya perlu bantuan langsung dari manusia"
- "Bisa transfer ke agen?"
"""
)


def check_admin_status():
    """
    Mengecek status admin berdasarkan jam operasional GMT+7.
    
    Returns:
        tuple: (status_message, is_operational_hours)
    """
    # Set timezone GMT+7
    gmt_plus_7 = pytz.timezone('Asia/Jakarta')
    current_time = datetime.now(gmt_plus_7)
    current_hour = current_time.hour
    
    # Jam operasional: 07.00 - 20.00
    if 7 <= current_hour < 20:
        return "Terhubung dengan admin 👨‍💼", True
    else:
        return "Di luar jam operasional (07.00-20.00 GMT+7) 🕐", False


class ConnectToAdminSchema(BaseModel):
    """
    Schema untuk menonaktifkan balasan AI dan mengalihkan ke agen manusia.
    Tool ini tidak memerlukan parameter input apapun.
    """

    tool_call_id: Annotated[str, InjectedToolCallId]


async def _aconnect_to_admin(
    tool_call_id: str,
    config: RunnableConfig,
):
    """
    Menonaktifkan balasan AI dan mengalihkan percakapan ke agen manusia secara asinkron.

    Args:
        tool_call_id: ID unik untuk tool call ini
        config: Konfigurasi runtime yang berisi chatroom_path

    Returns:
        Command: Objek command dengan update state dan pesan konfirmasi pengalihan

    Raises:
        Exception: Jika terjadi error dalam proses penonaktifan AI
    """
    try:
        # Log untuk analisis
        print("AI Reply disabled - User requested connection to admin")

        # Ambil chatroom path dari konfigurasi
        chat_room_path = config.get("configurable", {}).get("chatroom_path")

        if chat_room_path:
            # Nonaktifkan AI reply di sistem
            result = await turn_off_ai_reply(
                chat_room_path=chat_room_path,
                conversation_resume="User requested connection to admin",
            )

            if result is None:
                raise ValueError("Gagal menonaktifkan balasan AI di sistem")
        else:
            # Jika tidak ada chatroom_path, tetap lanjutkan dengan warning
            logging.warning(
                "chatroom_path tidak tersedia, AI reply tidak dinonaktifkan di database"
            )

        # Cek status admin berdasarkan jam operasional
        admin_status, is_operational = check_admin_status()
        
        # Buat pesan konfirmasi yang profesional dan ramah dengan status admin
        if is_operational:
            confirmation_message = (
                f"Baik Kak! 😊 {admin_status}\n\n"
                "Percakapan akan segera dialihkan ke admin kami. "
                "Admin biasanya merespon dalam *1-5 menit* selama jam operasional. "
                "Terima kasih atas kesabaran Kakak! 🙏\n\n"
                "_Pesan ini dibalas oleh Marta (Asisten Robot)._"
            )
        else:
            confirmation_message = (
                f"Baik Kak! 😊 Permintaan sudah diterima. {admin_status}\n\n"
                "⚠️ *Perhatian:* Admin kemungkinan *slow respon* atau *tidak merespon* "
                "karena di luar jam operasional (07.00-20.00 GMT+7).\n\n"
                "📅 Admin akan merespon pada *jam kerja berikutnya* (paling lambat keesokan harinya). "
                "Mohon bersabar ya Kak! 🙏\n\n"
                "_Pesan ini dibalas oleh Marta (Asisten Robot)._"
            )

        tool_message = ToolMessage(
            content=confirmation_message,
            tool_call_id=tool_call_id,
        )

        # Update state dengan flag AI disabled dan reason untuk tracking
        return Command(
            update={
                "messages": [tool_message],
                "connected_to_admin": True,
            },
        )

    except Exception as e:
        # Error umum lainnya - tetap coba alihkan ke manusia
        error_msg = (
            "Maaf, terjadi kesalahan teknis saat mengalihkan percakapan. "
            "Namun permintaan Anda telah dicatat dan akan segera ditangani oleh tim kami."
        )

        tool_message = ToolMessage(
            content=error_msg,
            tool_call_id=tool_call_id,
        )

        # Tetap set connected_to_admin untuk memastikan pengalihan
        return Command(
            update={
                "messages": [tool_message],
                "escalation_reason": f"Technical error: {str(e)}",
            },
        )


connect_to_admin_tool = StructuredTool.from_function(
    name="connect_to_admin",
    coroutine=_aconnect_to_admin,
    args_schema=ConnectToAdminSchema,
    description=_DESCRIPTION,
    return_direct=False,
)
