import asyncio
from typing import Dict, Any, Optional

from app.services.api_client import ideal_api_client


class UpdateUserInformationService:
    """Service untuk mengupdate informasi user melalui API Ideal."""

    async def update_city_group(
        self,
        chat_room_path: str,
        city_group: str,
        organization: str,
        organization_group: str,
    ) -> Optional[Dict[str, Any]]:
        """
        Update city group information untuk user secara asynchronous.

        Args:
            chat_room_path (str): Path chat room identifier
            city_group (str): Grup kota yang akan diupdate
            organization (str): Organisasi user
            organization_group (str): Grup organisasi user

        Returns:
            Optional[Dict[str, Any]]: Response JSON dari API atau None jika terjadi error

        Raises:
            httpx.HTTPStatusError: Jika terjadi error HTTP
        """
        url = "/api/public/update-client-citygroup"
        payload = {
            "chatRoomPath": chat_room_path,
            "cityGroup": city_group,
            "organization": organization,
            "organizationGroup": organization_group,
        }
        try:
            response = await ideal_api_client.post(url, json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error updating city group: {e}")
            return None

    async def update_name(
        self, chat_room_path: str, name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Update nama user secara asynchronous.

        Args:
            chat_room_path (str): Path chat room identifier
            name (str): Nama lengkap user yang akan diupdate

        Returns:
            Optional[Dict[str, Any]]: Response JSON dari API atau None jika terjadi error

        Raises:
            httpx.HTTPStatusError: Jika terjadi error HTTP
        """
        url = "/api/public/update-client-name"
        payload = {
            "chatRoomPath": chat_room_path,
            "name": name,
        }
        try:
            response = await ideal_api_client.post(url, json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error updating name: {e}")
            return None


if __name__ == "__main__":

    async def main():
        service = UpdateUserInformationService()

        # Example usage
        try:
            print("--- Update nama user ---")
            name_result = await service.update_name(
                chat_room_path="/projects/WLdKug7hau0MbRzKcnqg/chat_rooms/6285763705624",
                name="Budi Santoso",
            )
            print(f"Name update result: {name_result}")

            print("\n--- Update city group user ---")
            city_group_result = await service.update_city_group(
                chat_room_path="/projects/WLdKug7hau0MbRzKcnqg/chat_rooms/6285763705624",
                city_group="Jakarta",
                organization="Honda",
                organization_group="Dealer",
            )
            print(f"City group update result: {city_group_result}")

        except Exception as e:
            print(f"Error: {e}")

    asyncio.run(main())
