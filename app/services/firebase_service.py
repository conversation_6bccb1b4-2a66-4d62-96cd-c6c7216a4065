import asyncio

import firebase_admin
from dotenv import load_dotenv
from firebase_admin import credentials, firestore_async

load_dotenv()


def initialize_firebase(
    key_path: str = "./app/keys/firebase/ideal-trimitra-2620981b291f.json",
):
    # Check if the app is already initialized to prevent errors on hot reload
    try:
        firebase_admin.get_app()
        print("Firebase app already initialized")
    except ValueError:
        cred_path = key_path
        cred = credentials.Certificate(cred_path)
        firebase_admin.initialize_app(cred)
        print("Firebase app initialized")


def afirestore_client():
    """Returns an asynchronous Firestore client."""
    initialize_firebase()
    return firestore_async.client()
